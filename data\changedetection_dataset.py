"""Dataset class for change detection."""
import os
from data.base_dataset import BaseDataset, get_params, get_transform
from PIL import Image
import torch
import numpy as np


class ChangedetectionDataset(BaseDataset):
    """A dataset class for change detection dataset."""

    def __init__(self, opt):
        """Initialize this dataset class.
        Parameters:
            opt (Option class) -- stores all the experiment flags
        """
        BaseDataset.__init__(self, opt)
        self.dir_A = os.path.join(opt.dataroot, 'A')
        self.dir_B = os.path.join(opt.dataroot, 'B')
        self.dir_L = os.path.join(opt.dataroot, 'label')

        self.A_paths = sorted([os.path.join(self.dir_A, x) for x in os.listdir(self.dir_A)])
        self.B_paths = sorted([os.path.join(self.dir_B, x) for x in os.listdir(self.dir_B)])
        if not opt.istest:
            self.L_paths = sorted([os.path.join(self.dir_L, x) for x in os.listdir(self.dir_L)])
        else:
            self.L_paths = None

        self.transform = get_transform(opt)

    def __getitem__(self, index):
        """Return a data point and its metadata information.
        Parameters:
            index -- a random integer for data indexing
        Returns:
            A dictionary containing image data and metadata information.
        """
        try:
            A_path = self.A_paths[index]
            B_path = self.B_paths[index]

            if self.opt.use_rasterio:
                # 使用 rasterio 读取栅格数据
                A_img, profile_A = self.read_raster(A_path)
                if A_img is None:
                    raise RuntimeError(f"Failed to read image: {A_path}")

                B_img, _ = self.read_raster(B_path)
                if B_img is None:
                    raise RuntimeError(f"Failed to read image: {B_path}")

                # 确保数据类型和归一化
                A_img = A_img.astype(np.float32)
                B_img = B_img.astype(np.float32)
                if A_img.max() > 1:
                    A_img = A_img / 255.0
                if B_img.max() > 1:
                    B_img = B_img / 255.0

                # 应用 transform
                A = self.transform(A_img)
                B = self.transform(B_img)

                if not self.opt.istest and self.L_paths:
                    L_path = self.L_paths[index]
                    L_img, _ = self.read_raster(L_path)
                    if L_img is None:
                        raise RuntimeError(f"Failed to read label: {L_path}")
                    # 标签应该是单通道，值域为0/1
                    L = torch.from_numpy(L_img).long()
                    if L.dim() == 3:
                        L = L[0]  # 取第一个通道作为标签
                else:
                    L = torch.zeros(1)  # dummy label for test
            else:
                # 使用PIL读取图像
                A_img = Image.open(A_path).convert('RGB')
                B_img = Image.open(B_path).convert('RGB')

                # apply the same transform to both A and B
                transform_params = get_params(self.opt, A_img.size)
                A = self.transform(A_img)
                B = self.transform(B_img)

                if not self.opt.istest and self.L_paths:
                    L_path = self.L_paths[index]
                    L = Image.open(L_path)
                    if self.opt.input_nc == 1:
                        L = L.convert('L')
                    transform_L = get_transform(self.opt, normalize=False)
                    L = transform_L(L) * 255.0
                    L = L.long()
                else:
                    L = torch.zeros(1)
                profile_A = None

            # 确保所有tensor都是有效的
            if A is None or B is None or L is None:
                raise RuntimeError(f"Invalid tensor generated for index {index}")

            return {'A': A, 'B': B, 'L': L, 'A_paths': A_path, 'profile': profile_A}

        except Exception as e:
            print(f"Error processing index {index}: {str(e)}")
            # 返回一个有效的默认值，避免None
            default_size = (3, 256, 256) if self.opt.input_nc == 3 else (1, 256, 256)
            return {
                'A': torch.zeros(default_size),
                'B': torch.zeros(default_size),
                'L': torch.zeros(1, 256, 256) if not self.opt.istest else torch.zeros(1),
                'A_paths': A_path,
                'profile': None
            }

    def __len__(self):
        """Return the total number of images."""
        return len(self.A_paths)
