import os
import cv2
import numpy as np


def check_tiff_pixels_are_all_zero(directory_path="label"):
    """
    检查指定目录下所有 TIFF 图像的像素值是否全部为 0。

    Args:
        directory_path (str): 包含 TIFF 图像的目录路径。默认为 "label"。

    Returns:
        None: 直接打印结果。
    """
    if not os.path.isdir(directory_path):
        print(f"错误: 目录 '{directory_path}' 不存在。请确保路径正确。")
        return

    print(f"正在检查目录 '{directory_path}' 下的 TIFF 图像...\n")
    found_tiff = False

    for filename in os.listdir(directory_path):
        if filename.lower().endswith(('.tif', '.tiff')):
            found_tiff = True
            filepath = os.path.join(directory_path, filename)

            try:
                # 使用 OpenCV 读取图像。灰度图像会以单通道 NumPy 数组形式返回。
                # cv2.IMREAD_UNCHANGED 保持原始通道数和深度，对于8bit单通道图像仍会是单通道。
                # 对于8bit tif，imread会直接返回(H, W)或(H, W, C)的np.uint8数组
                img = cv2.imread(filepath, cv2.IMREAD_UNCHANGED)

                if img is None:
                    print(f"  - '{filename}': 无法读取图像。可能是文件损坏或格式不受支持。")
                    continue

                # 确保图像不是空的 (例如，读取失败但没有抛出异常的情况)
                if img.size == 0:
                    print(f"  - '{filename}': 图像为空（没有像素数据）。")
                    continue

                # 将图像展平为一维数组，然后检查所有元素是否都为0
                # img.flatten() 创建一个视图，不会复制数据
                # (img == 0).all() 是更直接且推荐的方式，它会检查数组中的所有元素
                # 如果图像是多通道的（例如RGB），它会检查所有通道的所有像素是否都为0
                if (img == 0).all():
                    print(f"  - '{filename}': ✅ **所有像素值均为 0。**")
                else:
                    # 检查是否有非零像素，如果有，则打印出来
                    non_zero_pixels = np.count_nonzero(img)
                    print(f"  - '{filename}': ❌ 包含非零像素 (总共有 {non_zero_pixels} 个非零像素)。")

            except Exception as e:
                print(f"  - '{filename}': 处理时发生错误：{e}")

    if not found_tiff:
        print(f"在目录 '{directory_path}' 中未找到任何 .tif 或 .tiff 图像。")


# --- 使用示例 ---
if __name__ == "__main__":
    # 确保你的脚本同目录下有一个名为 'label' 的文件夹，
    # 并在其中放入你的 8-bit TIFF 图像进行测试。

    # 例如，你可以创建一个空的 8-bit TIFF 图像进行测试：
    # from PIL import Image
    # import numpy as np
    # Image.fromarray(np.zeros((100, 100), dtype=np.uint8)).save("label/empty_image.tif")
    # Image.fromarray(np.ones((100, 100), dtype=np.uint8)).save("label/filled_image.tif")
    # Image.fromarray(np.random.randint(0, 256, (100, 100), dtype=np.uint8)).save("label/random_image.tif")

    # 调用函数开始检查
    check_tiff_pixels_are_all_zero(r"F:\CD\STANet-master\datasat\label")