import rasterio
import numpy as np
import os

def save_pred_raster(pred, ref_path, save_path):
    """
    保存带投影的预测二值图为GeoTIFF栅格数据。
    pred: numpy数组，shape为(H, W)或(1, H, W)或(B, 1, H, W)
    ref_path: 参考影像路径（如A或B时相的tif），用于获取投影和地理变换信息
    save_path: 输出tif路径
    """
    # 读取参考影像的投影和仿射变换
    with rasterio.open(ref_path) as src:
        profile = src.profile.copy()
        transform = src.transform
        crs = src.crs
        height = src.height
        width = src.width

    # 处理pred shape
    if pred.ndim == 4:
        pred = pred[0, 0]
    elif pred.ndim == 3:
        pred = pred[0]
    # 保证shape为(H, W)
    pred = np.array(pred).astype(np.uint8)
    if pred.shape != (height, width):
        from skimage.transform import resize
        pred = resize(pred, (height, width), order=0, preserve_range=True, anti_aliasing=False).astype(np.uint8)

    # 更新profile
    profile.update({
        'count': 1,
        'dtype': 'uint8',
        'compress': 'lzw',
        'height': height,
        'width': width,
        'transform': transform,
        'crs': crs
    })

    # 保存tif
    with rasterio.open(save_path, 'w', **profile) as dst:
        dst.write(pred, 1)

if __name__ == '__main__':
    # 用法示例
    pred = np.load('debug_hist/pred_L.npy')  # 假设预测结果已保存为npy
    ref_path = 'datasat/A/00000.tif'         # 参考影像路径
    save_path = 'results/pred_00000.tif'     # 输出路径
    save_pred_raster(pred, ref_path, save_path)

