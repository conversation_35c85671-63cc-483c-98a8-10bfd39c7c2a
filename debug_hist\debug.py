import numpy as np

# 加载预测和标签的直方图
pred_hist = np.load('F:\CD\STANet-master\debug_hist/pred_L_hist.npy')
label_hist = np.load('F:\CD\STANet-master\debug_hist/label_hist.npy')

print("预测图像类别分布（pred_L）：")
print(f"0类像素数: {pred_hist[0]}")
print(f"1类像素数: {pred_hist[1]}")
print("标签图像类别分布（label）：")
print(f"0类像素数: {label_hist[0]}")
print(f"1类像素数: {label_hist[1]}")

# 也可以计算比例
total_pred = pred_hist.sum()
total_label = label_hist.sum()
print(f"预测0类比例: {pred_hist[0]/total_pred:.4f}, 1类比例: {pred_hist[1]/total_pred:.4f}")
print(f"标签0类比例: {label_hist[0]/total_label:.4f}, 1类比例: {label_hist[1]/total_label:.4f}")